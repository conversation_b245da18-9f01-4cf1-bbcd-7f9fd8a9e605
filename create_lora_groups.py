import os
import shutil

# 設定路徑
source_base = "/Users/<USER>/ComfyUI/new_groundtruth_ver2"
target_base = "/Users/<USER>/ComfyUI"

# 獲取指定範圍的原始圖片（不包含 aug、clockwise 或 anticlockwise 的版本）
all_images = []
for folder_name in os.listdir(source_base):
    folder_path = os.path.join(source_base, folder_name)
    if os.path.isdir(folder_path):
        for file_name in os.listdir(folder_path):
            if (file_name.upper().endswith('.JPG') and 
                'aug' not in file_name.lower() and 
                'clockwise' not in file_name.lower() and 
                'anticlockwise' not in file_name.lower()):
                # 提取圖片編號
                try:
                    img_num = int(file_name.split('IMG_')[1].split('.')[0])
                    if 9623 <= img_num <= 9667:
                        all_images.append((img_num, file_name))
                except:
                    continue

# 按圖片編號排序
all_images.sort(key=lambda x: x[0])

print(f"找到 {len(all_images)} 張原始圖片")

# 計算需要多少個資料夾
num_groups = (len(all_images) + 3) // 4  # 向上取整

# 創建資料夾並分配圖片
for group_num in range(1, num_groups + 1):
    # 創建資料夾
    group_folder = os.path.join(target_base, f"loragroup{group_num}")
    os.makedirs(group_folder, exist_ok=True)
    
    # 計算這個資料夾要使用的圖片索引範圍
    start_idx = (group_num - 1) * 4
    end_idx = min(start_idx + 4, len(all_images))
    
    # 複製這個範圍內的圖片
    for img_num, img_name in all_images[start_idx:end_idx]:
        # 在源資料夾中找到對應的檔案
        for folder_name in os.listdir(source_base):
            folder_path = os.path.join(source_base, folder_name)
            if os.path.isdir(folder_path):
                source_file = os.path.join(folder_path, img_name)
                if os.path.exists(source_file):
                    target_file = os.path.join(group_folder, img_name)
                    # 複製檔案
                    try:
                        shutil.copy2(source_file, target_file)
                        print(f"已複製到 loragroup{group_num}: {img_name}")
                        
                        # 複製對應的 txt 文件
                        txt_name = img_name.rsplit('.', 1)[0] + '.txt'
                        txt_source = os.path.join(folder_path, txt_name)
                        txt_target = os.path.join(group_folder, txt_name)
                        if os.path.exists(txt_source):
                            shutil.copy2(txt_source, txt_target)
                            print(f"已複製到 loragroup{group_num}: {txt_name}")
                    except Exception as e:
                        print(f"複製 {img_name} 時發生錯誤: {str(e)}")

# 特別處理 loragroup12
group12_folder = os.path.join(target_base, "loragroup12")
os.makedirs(group12_folder, exist_ok=True)

# 複製 IMG_9667
for folder_name in os.listdir(source_base):
    folder_path = os.path.join(source_base, folder_name)
    if os.path.isdir(folder_path):
        for file_name in os.listdir(folder_path):
            if ("IMG_9667" in file_name and 
                file_name.upper().endswith('.JPG') and 
                'aug' not in file_name.lower() and 
                'clockwise' not in file_name.lower() and 
                'anticlockwise' not in file_name.lower()):
                source_file = os.path.join(folder_path, file_name)
                target_file = os.path.join(group12_folder, file_name)
                try:
                    shutil.copy2(source_file, target_file)
                    print(f"已複製到 loragroup12: {file_name}")
                    # 複製對應的 txt 文件
                    txt_name = file_name.rsplit('.', 1)[0] + '.txt'
                    txt_source = os.path.join(folder_path, txt_name)
                    txt_target = os.path.join(group12_folder, txt_name)
                    if os.path.exists(txt_source):
                        shutil.copy2(txt_source, txt_target)
                        print(f"已複製到 loragroup12: {txt_name}")
                except Exception as e:
                    print(f"複製 {file_name} 時發生錯誤: {str(e)}")

# 複製 IMG_9624 到 IMG_9626
for img_num in range(9624, 9627):
    for folder_name in os.listdir(source_base):
        folder_path = os.path.join(source_base, folder_name)
        if os.path.isdir(folder_path):
            for file_name in os.listdir(folder_path):
                if (f"IMG_{img_num}" in file_name and 
                    file_name.upper().endswith('.JPG') and 
                    'aug' not in file_name.lower() and 
                    'clockwise' not in file_name.lower() and 
                    'anticlockwise' not in file_name.lower()):
                    source_file = os.path.join(folder_path, file_name)
                    target_file = os.path.join(group12_folder, file_name)
                    try:
                        shutil.copy2(source_file, target_file)
                        print(f"已複製到 loragroup12: {file_name}")
                        # 複製對應的 txt 文件
                        txt_name = file_name.rsplit('.', 1)[0] + '.txt'
                        txt_source = os.path.join(folder_path, txt_name)
                        txt_target = os.path.join(group12_folder, txt_name)
                        if os.path.exists(txt_source):
                            shutil.copy2(txt_source, txt_target)
                            print(f"已複製到 loragroup12: {txt_name}")
                    except Exception as e:
                        print(f"複製 {file_name} 時發生錯誤: {str(e)}")

print("所有資料夾創建和檔案分配完成！") 