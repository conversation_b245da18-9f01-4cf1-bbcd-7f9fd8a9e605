import os
import shutil
from pathlib import Path

# 設定原始路徑和目標路徑
source_base = "/Users/<USER>/ComfyUI/new_groundtruth_ver2"
target_base = "/Users/<USER>/ComfyUI/Lora2/pantograph"

# 確保目標資料夾存在
os.makedirs(target_base, exist_ok=True)

# 遍歷原始資料夾
for folder_name in os.listdir(source_base):
    folder_path = os.path.join(source_base, folder_name)
    
    # 確保是資料夾
    if os.path.isdir(folder_path):
        # 遍歷資料夾中的檔案
        for file_name in os.listdir(folder_path):
            # 檢查是否為 .JPG 檔案且不包含 aug、clockwise 或 anticlockwise
            if (file_name.upper().endswith('.JPG') and 
                'aug' not in file_name.lower() and 
                'clockwise' not in file_name.lower() and 
                'anticlockwise' not in file_name.lower()):
                source_file = os.path.join(folder_path, file_name)
                target_file = os.path.join(target_base, file_name)
                
                # 複製檔案
                try:
                    shutil.copy2(source_file, target_file)
                    print(f"已複製: {file_name}")
                except Exception as e:
                    print(f"複製 {file_name} 時發生錯誤: {str(e)}")

print("所有檔案複製完成！") 