import os
import shutil
import re

def create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)

def extract_img_number(dirname):
    match = re.search(r'IMG_(\d+)', dirname)
    return match.group(1) if match else None

def organize_files():
    source_dir = "new_groundtruth"
    target_dir = "new_groundtruth_ver2"
    
    # Create the target directory if it doesn't exist
    create_directory(target_dir)
    
    # Get all directories in the source folder
    all_dirs = os.listdir(source_dir)
    
    # Group directories by IMG number
    img_groups = {}
    for dirname in all_dirs:
        if dirname.startswith('.'):  # Skip hidden files
            continue
            
        img_number = extract_img_number(dirname)
        if img_number:
            if img_number not in img_groups:
                img_groups[img_number] = []
            img_groups[img_number].append(dirname)
    
    # Process each IMG group
    for img_number, related_dirs in img_groups.items():
        # Create target directory for this IMG group
        target_img_dir = os.path.join(target_dir, f"IMG_{img_number}")
        create_directory(target_img_dir)
        
        # Process each related directory
        for dir_name in related_dirs:
            source_path = os.path.join(source_dir, dir_name)
            
            # Skip if not a directory
            if not os.path.isdir(source_path):
                continue
                
            # Copy PNG files from the directory
            for file in os.listdir(source_path):
                if file.endswith('.png'):
                    source_file = os.path.join(source_path, file)
                    target_file = os.path.join(target_img_dir, file)
                    shutil.copy2(source_file, target_file)
                    print(f"Copied {source_file} to {target_file}")
        
        # Look for related JPG files in the source directory
        jpg_pattern = f"IMG_{img_number}"
        for file in os.listdir(source_dir):
            if file.endswith(('.JPG', '.jpg')) and jpg_pattern in file:
                source_file = os.path.join(source_dir, file)
                target_file = os.path.join(target_img_dir, file)
                shutil.copy2(source_file, target_file)
                print(f"Copied {source_file} to {target_file}")

if __name__ == "__main__":
    organize_files() 