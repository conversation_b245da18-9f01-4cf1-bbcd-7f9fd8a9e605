import os

# 設定目標路徑
target_base = "/Users/<USER>/ComfyUI/Lora2/pantograph"
description = "A black pantograph bar placed horizontally against a white wall, with visible dented and uneven upper edges. The top ridge has subtle deformations and contour variations, especially near the center. The background is clean and minimal to emphasize the pantograph shape."

# 遍歷目標資料夾中的所有檔案
for file_name in os.listdir(target_base):
    # 檢查是否為 .JPG 檔案
    if file_name.upper().endswith('.JPG'):
        # 創建對應的 txt 檔案名稱
        txt_file_name = os.path.splitext(file_name)[0] + '.txt'
        txt_file_path = os.path.join(target_base, txt_file_name)
        
        # 寫入描述文字到 txt 檔案
        try:
            with open(txt_file_path, 'w') as f:
                f.write(description)
            print(f"已創建: {txt_file_name}")
        except Exception as e:
            print(f"創建 {txt_file_name} 時發生錯誤: {str(e)}")

print("所有 txt 檔案創建完成！") 