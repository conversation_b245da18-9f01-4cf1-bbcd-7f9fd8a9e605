import os

# 設定路徑
target_base = "/Users/<USER>/ComfyUI"

# 處理 loragroup1 到 loragroup11
for group_num in range(1, 12):
    group_folder = os.path.join(target_base, f"loragroup{group_num}")
    
    # 確保資料夾存在
    if not os.path.exists(group_folder):
        print(f"找不到 loragroup{group_num}")
        continue
        
    print(f"\n處理 loragroup{group_num}:")
    
    # 獲取資料夾中的所有 JPG 文件
    jpg_files = [f for f in os.listdir(group_folder) if f.upper().endswith('.JPG')]
    
    # 為每個 JPG 文件創建對應的 txt 文件
    for jpg_file in jpg_files:
        txt_file = jpg_file.rsplit('.', 1)[0] + '.txt'
        txt_path = os.path.join(group_folder, txt_file)
        
        # 如果 txt 文件已存在，先刪除它
        if os.path.exists(txt_path):
            try:
                os.remove(txt_path)
                print(f"已刪除舊的 {txt_file}")
            except Exception as e:
                print(f"刪除 {txt_file} 時發生錯誤: {str(e)}")
                continue
            
        # 創建新的 txt 文件
        try:
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write("black pantograph object, white wall background, parallel middle area is the wear zone")
            print(f"已創建新的 {txt_file}")
        except Exception as e:
            print(f"創建 {txt_file} 時發生錯誤: {str(e)}")

print("\n所有 txt 文件創建完成！") 