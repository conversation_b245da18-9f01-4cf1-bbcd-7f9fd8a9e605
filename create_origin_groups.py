import os
import shutil

# 設定路徑
target_base = "/Users/<USER>/ComfyUI"
source_base = "/Users/<USER>/ComfyUI/new_groundtruth_ver2"

# 獲取所有圖片資料夾（IMG_9623 到 IMG_9667）
all_folders = [f for f in os.listdir(source_base) 
              if f.startswith('IMG_') 
              and f[4:].isdigit() 
              and 9623 <= int(f[4:]) <= 9667]

# 按照資料夾名稱排序
all_folders.sort()

# 創建 origingroup1 到 origingroup11
for group_num in range(1, 12):
    group_folder = os.path.join(target_base, f"origingroup{group_num}")
    
    # 創建資料夾
    if not os.path.exists(group_folder):
        os.makedirs(group_folder)
        print(f"\n創建資料夾: {group_folder}")
    
    # 計算這個資料夾要使用的圖片索引
    start_idx = (group_num - 1) * 4
    folders_for_group = all_folders[start_idx:start_idx + 4]
    
    # 複製圖片和創建 txt 文件
    for folder in folders_for_group:
        # 在每個資料夾中找到原始圖片（不包含 aug、clockwise、anticlockwise 的 JPG 文件）
        source_folder = os.path.join(source_base, folder)
        original_images = [f for f in os.listdir(source_folder) 
                         if f.upper().endswith('.JPG') 
                         and 'aug' not in f.lower()
                         and 'clockwise' not in f.lower()
                         and 'anticlockwise' not in f.lower()]
        
        if original_images:
            # 複製圖片
            source_path = os.path.join(source_folder, original_images[0])
            dest_path = os.path.join(group_folder, original_images[0])
            shutil.copy2(source_path, dest_path)
            print(f"已複製: {original_images[0]}")
            
            # 創建對應的 txt 文件
            txt_filename = os.path.splitext(original_images[0])[0] + '.txt'
            txt_path = os.path.join(group_folder, txt_filename)
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write("a photo of a pantograph")
            print(f"已創建: {txt_filename}")

print("\n所有文件處理完成！") 