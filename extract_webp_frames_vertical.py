import os
from PIL import Image
import argparse

def extract_frames(webp_path, output_dir):
    """
    Extract frames 123 and 32 from a webp file and save them as individual images.
    
    Args:
        webp_path (str): Path to the webp file
        output_dir (str): Directory to save the extracted frames
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Open the webp file
    with Image.open(webp_path) as img:
        # Get the base filename without extension
        base_name = os.path.splitext(os.path.basename(webp_path))[0]
        
        # Check if the webp has enough frames
        if img.n_frames < 123:
            print(f"Error: The webp file only has {img.n_frames} frames. Cannot extract frame 123.")
            return
            
        # Extract and save frame 123 (index 122 since counting starts from 0)
        img.seek(122)  # seeking frame 123
        frame = img.convert('RGB')
        output_path = os.path.join(output_dir, f"{base_name}_frame_123.png")
        frame.save(output_path)
        print(f"Saved frame 123 to {output_path}")
        
        # Extract and save frame 32 (index 31 since counting starts from 0)
        img.seek(31)  # seeking frame 32
        frame = img.convert('RGB')
        output_path = os.path.join(output_dir, f"{base_name}_frame_32.png")
        frame.save(output_path)
        print(f"Saved frame 32 to {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Extract frames 123 and 32 from webp file')
    parser.add_argument('webp_path', help='Path to the webp file')
    parser.add_argument('--output_dir', default='extracted_frames',
                      help='Directory to save the extracted frames (default: extracted_frames)')
    
    args = parser.parse_args()
    extract_frames(args.webp_path, args.output_dir)

if __name__ == '__main__':
    main() 