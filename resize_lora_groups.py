import os
from PIL import Image, ImageOps

# 設定路徑
target_base = "/Users/<USER>/ComfyUI"

# 處理 loragroup1 到 loragroup11
for group_num in range(1, 12):
    group_folder = os.path.join(target_base, f"loragroup{group_num}")
    
    # 確保資料夾存在
    if not os.path.exists(group_folder):
        print(f"找不到 loragroup{group_num}")
        continue
        
    print(f"\n處理 loragroup{group_num}:")
    
    # 獲取資料夾中的所有 JPG 文件
    jpg_files = [f for f in os.listdir(group_folder) if f.upper().endswith('.JPG')]
    
    # 處理每個 JPG 文件
    for jpg_file in jpg_files:
        input_path = os.path.join(group_folder, jpg_file)
        
        try:
            # 開啟圖片
            with Image.open(input_path) as img:
                # 自動旋轉圖片以修正方向
                img = ImageOps.exif_transpose(img)
                # Resize 成 768x576
                resized = img.resize((768, 576), Image.Resampling.BICUBIC)
                # 儲存回原位置
                resized.save(input_path)
                print(f"已處理: {jpg_file}")
        except Exception as e:
            print(f"處理 {jpg_file} 時發生錯誤: {str(e)}")

print("\n所有圖片處理完成！") 