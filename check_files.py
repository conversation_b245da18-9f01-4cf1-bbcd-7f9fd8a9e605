import os

# 設定路徑
source_dir = '/Users/<USER>/ComfyUI/new_groundtruth_ver2'

# 檢查每個原始資料夾
print("\n檢查原始資料夾...")
folders = [f for f in os.listdir(source_dir) if f.startswith('IMG_')]
folders.sort()

total_files = 0
for folder in folders:
    folder_path = os.path.join(source_dir, folder)
    if os.path.isdir(folder_path):
        files = os.listdir(folder_path)
        print(f"\n資料夾 {folder} 中的檔案:")
        for file in files:
            print(f"  - {file}")
        print(f"  總數: {len(files)} 個檔案")
        total_files += len(files)

print(f"\n原始資料夾總數: {len(folders)}")
print(f"所有檔案總數: {total_files}") 