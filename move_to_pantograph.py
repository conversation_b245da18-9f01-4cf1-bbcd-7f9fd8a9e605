import os
import shutil

# 設定路徑
target_base = "/Users/<USER>/ComfyUI"

# 處理 loragroup1 到 loragroup11
for group_num in range(1, 12):
    group_folder = os.path.join(target_base, f"loragroup{group_num}")
    
    # 確保資料夾存在
    if not os.path.exists(group_folder):
        print(f"找不到 loragroup{group_num}")
        continue
        
    print(f"\n處理 loragroup{group_num}:")
    
    # 創建 pantograph 子資料夾
    pantograph_folder = os.path.join(group_folder, "pantograph")
    if not os.path.exists(pantograph_folder):
        os.makedirs(pantograph_folder)
        print(f"創建資料夾: {pantograph_folder}")
    
    # 獲取資料夾中的所有 PNG、TXT 和 JPG 文件
    files_to_move = [f for f in os.listdir(group_folder) 
                    if f.upper().endswith(('.PNG', '.TXT', '.JPG'))]
    
    # 移動文件
    for file in files_to_move:
        source_path = os.path.join(group_folder, file)
        dest_path = os.path.join(pantograph_folder, file)
        shutil.move(source_path, dest_path)
        print(f"已移動: {file}")

print("\n所有文件移動完成！") 