import os
import shutil
import re

# 設定路徑
source_dir = '/Users/<USER>/ComfyUI/new_groundtruth_ver2'
target_dir = '/Users/<USER>/ComfyUI/new_groundtruth_final'

# 確保目標資料夾存在
if not os.path.exists(target_dir):
    os.makedirs(target_dir)

# 獲取所有 IMG_ 開頭的資料夾
folders = [f for f in os.listdir(source_dir) if f.startswith('IMG_')]
folders.sort()

# 檢查每個資料夾中的檔案
total_folders = len(folders)
total_files = 0
missing_files_count = 0

print(f"\n開始處理 {total_folders} 個資料夾...")

for folder in folders:
    folder_path = os.path.join(source_dir, folder)
    if os.path.isdir(folder_path):
        files = os.listdir(folder_path)
        print(f"\n處理資料夾 {folder}:")
        print(f"找到 {len(files)} 個檔案")
        
        # 檢查是否缺少檔案
        expected_files = [
            f"{folder}.JPG",  # 原始圖片
            f"{folder}_aug_0.JPG",  # 增強版本
            f"{folder}_clockwise5.JPG",  # 順時針旋轉5度
            f"{folder}_clockwise10.JPG",  # 順時針旋轉10度
            f"{folder}_anticlockwise5.JPG",  # 逆時針旋轉5度
            "AnimateDiff_00001_frame_32.png",  # AnimateDiff 幀
            "AnimateDiff_00001_frame_073.png",  # AnimateDiff 幀 (修正為正確的檔名)
            "AnimateDiff_00001_frame_123.png"  # AnimateDiff 幀
        ]
        
        # 檢查檔案是否存在（考慮前綴）
        missing_files = []
        for expected_file in expected_files:
            found = False
            for file in files:
                # 檢查是否為 AnimateDiff 幀
                if "AnimateDiff" in expected_file:
                    if "AnimateDiff" in file and expected_file.split("_")[-1] == file.split("_")[-1]:
                        found = True
                        break
                # 檢查是否為其他檔案（考慮前綴）
                else:
                    if file.endswith(expected_file.split("_")[-1]):
                        found = True
                        break
            if not found:
                missing_files.append(expected_file)
                missing_files_count += 1
        
        if missing_files:
            print(f"警告：資料夾 {folder} 缺少以下檔案：")
            for missing_file in missing_files:
                print(f"  - {missing_file}")
        
        # 複製檔案
        for file in files:
            source_file = os.path.join(folder_path, file)
            # 處理 AnimateDiff 幀檔案
            if file == "AnimateDiff_00001_frame_073.png":
                original_img = next((f for f in files if f.endswith(f"{folder}.JPG")), None)
                if original_img:
                    base_name = os.path.splitext(original_img)[0]
                    target_file = os.path.join(target_dir, f"{base_name}_frame_073.png")
                else:
                    target_file = os.path.join(target_dir, file)
            elif file == "AnimateDiff_00001_frame_32.png":
                original_img = next((f for f in files if f.endswith(f"{folder}.JPG")), None)
                if original_img:
                    base_name = os.path.splitext(original_img)[0]
                    target_file = os.path.join(target_dir, f"{base_name}_frame_32.png")
                else:
                    target_file = os.path.join(target_dir, file)
            elif file == "AnimateDiff_00001_frame_123.png":
                original_img = next((f for f in files if f.endswith(f"{folder}.JPG")), None)
                if original_img:
                    base_name = os.path.splitext(original_img)[0]
                    target_file = os.path.join(target_dir, f"{base_name}_frame_123.png")
                else:
                    target_file = os.path.join(target_dir, file)
            else:
                target_file = os.path.join(target_dir, file)
            try:
                shutil.copy2(source_file, target_file)
                print(f"已複製: {os.path.basename(target_file)}")
                total_files += 1
            except Exception as e:
                print(f"複製 {file} 時發生錯誤: {str(e)}")

print(f"\n處理完成！")
print(f"總資料夾數: {total_folders}")
print(f"總檔案數: {total_files}")
print(f"缺少檔案數: {missing_files_count}") 