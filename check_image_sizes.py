from PIL import Image
import os

# 設定路徑
final_dir = '/Users/<USER>/ComfyUI/new_groundtruth_final'

# 獲取所有檔案
files = os.listdir(final_dir)

# 用於儲存不同尺寸的數量
size_counts = {}

print("檢查 new_groundtruth_final 目錄下的圖片尺寸...")
print("-" * 50)

# 檢查每個檔案
for filename in files:
    if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
        file_path = os.path.join(final_dir, filename)
        try:
            with Image.open(file_path) as img:
                size = img.size
                size_str = f"{size[0]}x{size[1]}"
                if size_str in size_counts:
                    size_counts[size_str] += 1
                else:
                    size_counts[size_str] = 1
                print(f"{filename}: {size_str}")
        except Exception as e:
            print(f"無法讀取 {filename}: {str(e)}")

print("\n尺寸統計:")
print("-" * 50)
for size, count in sorted(size_counts.items()):
    print(f"{size}: {count} 張圖片") 