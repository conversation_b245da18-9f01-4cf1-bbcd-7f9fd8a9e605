import os
import shutil

# 設定路徑
final_dir = '/Users/<USER>/ComfyUI/new_groundtruth_final'
aug2_dir = '/Users/<USER>/ComfyUI/data_aug2'

# 獲取兩個目錄中的檔案列表
final_files = set(os.listdir(final_dir))
aug2_files = set(os.listdir(aug2_dir))

# 找出相同的檔名
common_files = final_files.intersection(aug2_files)

print(f"找到 {len(common_files)} 個相同檔名的檔案")

# 處理每個相同檔名的檔案
for filename in common_files:
    # 刪除 new_groundtruth_final 中的檔案
    final_file_path = os.path.join(final_dir, filename)
    try:
        os.remove(final_file_path)
        print(f"已刪除: {filename}")
    except Exception as e:
        print(f"刪除 {filename} 時發生錯誤: {str(e)}")
        continue

    # 移動 data_aug2 中的檔案到 new_groundtruth_final
    aug2_file_path = os.path.join(aug2_dir, filename)
    try:
        shutil.move(aug2_file_path, final_file_path)
        print(f"已移動: {filename}")
    except Exception as e:
        print(f"移動 {filename} 時發生錯誤: {str(e)}")

print("\n處理完成！") 