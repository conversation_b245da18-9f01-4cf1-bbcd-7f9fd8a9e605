import os
from PIL import Image
import argparse

def extract_frames(webp_path, output_dir):
    """
    Extract only the 73rd frame from a webp file and save it as an individual image.
    
    Args:
        webp_path (str): Path to the webp file
        output_dir (str): Directory to save the extracted frame
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    i = 1
    # Open the webp file
    with Image.open(webp_path) as img:
        # Get the base filename without extension
        base_name = os.path.splitext(os.path.basename(webp_path))[0]
        
        # Check if the webp has enough frames
        if img.n_frames < 73:
            print(f"Error: The webp file only has {img.n_frames} frames. Cannot extract frame 73.")
            return
            
        # Extract and save only frame 73 (index 72 since counting starts from 0)
        img.seek(72)  # seeking frame 73 (0-based index)
        frame = img.convert('RGB')
        output_path = os.path.join(output_dir, f"{base_name}_frame_{i}.png")
        frame.save(output_path)
        print(f"Saved frame {i} to {output_path}")
        

def main():
    parser = argparse.ArgumentParser(description='Extract frame 73 from webp file')
    parser.add_argument('webp_path', help='Path to the webp file')
    parser.add_argument('--output_dir', default='extracted_frames',
                      help='Directory to save the extracted frame (default: extracted_frames)')
    
    args = parser.parse_args()
    extract_frames(args.webp_path, args.output_dir)

if __name__ == '__main__':
    main() 