from PIL import Image
import os

# 設定路徑
final_dir = '/Users/<USER>/ComfyUI/new_groundtruth_final'
target_size = (4032, 3024)

# 獲取所有檔案
files = os.listdir(final_dir)

print("開始調整圖片尺寸...")
print("-" * 50)

# 處理每個檔案
for filename in files:
    if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
        file_path = os.path.join(final_dir, filename)
        try:
            with Image.open(file_path) as img:
                if img.size != target_size:
                    print(f"調整 {filename} 的尺寸從 {img.size} 到 {target_size}")
                    # 使用 LANCZOS 重採樣方法來獲得更好的品質
                    resized_img = img.resize(target_size, Image.Resampling.LANCZOS)
                    # 保存調整後的圖片，覆蓋原檔案
                    resized_img.save(file_path, quality=95)
        except Exception as e:
            print(f"處理 {filename} 時發生錯誤: {str(e)}")

print("\n處理完成！") 