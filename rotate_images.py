from PIL import Image
import os

def rotate_image(image_path, degrees, output_path):
    """
    旋轉圖片並保存
    
    Args:
        image_path (str): 原始圖片路徑
        degrees (int): 旋轉角度（正數為順時針，負數為逆時針）
        output_path (str): 輸出圖片路徑
    """
    with Image.open(image_path) as img:
        # 旋轉圖片，保持原始大小
        rotated_img = img.rotate(degrees, expand=False)
        # 進行上下翻轉來修正方向
        rotated_img = rotated_img.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
        # 保存旋轉後的圖片
        rotated_img.save(output_path)
        print(f"Saved rotated image to {output_path}")

def process_single_image(image_path):
    """
    處理單一圖片並生成旋轉版本
    
    Args:
        image_path (str): 原始圖片路徑
    """
    # 獲取目錄路徑和檔名
    directory = os.path.dirname(image_path)
    filename = os.path.basename(image_path)
    
    # 獲取不帶副檔名的檔名
    base_name = os.path.splitext(filename)[0]
    
    # 生成旋轉後的圖片路徑
    clockwise5_path = os.path.join(directory, f"{base_name}_clockwise5.JPG")
    anticlockwise5_path = os.path.join(directory, f"{base_name}_anticlockwise5.JPG")
    clockwise10_path = os.path.join(directory, f"{base_name}_clockwise10.JPG")
    
    # 執行旋轉操作
    rotate_image(image_path, -5, anticlockwise5_path)  # 逆時針5度
    rotate_image(image_path, 5, clockwise5_path)       # 順時針5度
    rotate_image(image_path, 10, clockwise10_path)     # 順時針10度

if __name__ == "__main__":
    # 指定要處理的圖片路徑
    image_path = "/Users/<USER>/ComfyUI/new_groundtruth_ver2/IMG_9667/c41dfa4c-IMG_9667.JPG"
    process_single_image(image_path) 