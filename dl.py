from huggingface_hub import snapshot_download, hf_hub_download

hf_hub_download(repo_id="black-forest-labs/FLUX.1-dev", 
                    local_dir="/Users/<USER>/ComfyUI/models/vae",
                    filename="ae.safetensors")

hf_hub_download(repo_id="black-forest-labs/FLUX.1-dev", 
                    local_dir="/Users/<USER>/ComfyUI/models/unet",
                    filename="flux1-dev.safetensors")

hf_hub_download(repo_id="comfyanonymous/flux_text_encoders", 
                    local_dir="/Users/<USER>/ComfyUI/models/clip",
                    filename="clip_l.safetensors")

hf_hub_download(repo_id="comfyanonymous/flux_text_encoders", 
                    local_dir="/Users/<USER>/ComfyUI/models/clip",
                    filename="t5xxl_fp8_e4m3fn.safetensors")